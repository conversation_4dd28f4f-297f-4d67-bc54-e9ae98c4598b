# Changelog

All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [3.2.1] - 2023-04-27

* Fixed Newtonsoft DLL when compiling with netstandard 2.0.

## [3.2.0] - 2023-04-19

* Fixed Newtonsoft DLL public key token so it properly supports assembly strong name.
* Support Newtonsoft's `JsonSelectSettings.RegexMatchTimeout`.

## [3.1.0] - 2023-02-28

* Updated AOT and Editor DLLs to corresponds to Newtonsoft.Json version 13.0.2

## [3.0.2] - 2022-03-29

* Removed test code from package.

## [3.0.1] - 2022-02-21

* Updated license file

## [3.0.0] - 2022-01-27

* Updated Documentation

## [3.0.0-preview.1] - 2022-01-25

* Updated AOT and Editor DLLs to corresponds to Newtonsoft.Json version 13.0.1
* Removed deprecated Portable DLL
* Updated README
* Updated Documentation

## [2.0.2] - 2020-10-04

* Updated README
* Added package signature for validation

## [2.0.1-preview.1] - 2020-11-14

* Updated README and Internal Fixtures

## [2.0.0] - 2020-04-20

### This is the release of *Unity Package Nuget.NewtonsoftJson* v2.0.0.

* Updated dll to AOT compatible version to allow for IL2CPP compilation platform targets
* Added dll to Portable compatible version to allow for additional platform targets
* Updated associated Package Documents

## [2.0.0-preview] - 2019-11-14

### This is the preview of *Unity Package Nuget.NewtonsoftJson* v2.0.0-preview.

* Changed dll to AOT compatible version to allow for IL2CPP compilation platform targets

## [1.1.2] - 2019-10-31

### This is the release of *Unity Package Nuget.NewtonsoftJson* v1.1.2.

* Fixed tests for 2018.4.

## [1.1.1] - 2019-10-30

### This is the release of *Unity Package Nuget.NewtonsoftJson* v1.1.1.

* Added some sanity tests to catch issues in the CI.

## [1.1.0] - 2019-10-29

### This is the release of *Unity Package Nuget.NewtonsoftJson* v1.1.0.

* Use .net standard 2.0 version of the dll.
* Renamed the dll to match the assembly name. Users will need to update their assembly references.

## [1.1.0-preview.1] - 2019-08-09

### This is the first preview of *Unity Package Nuget.Newtonsoft.Json* version 1.1.0.

* Updated to bug fix release 12.0.2 of Newtonsoft Json.
* Updated package description.

## [1.0.1-preview.2] - 2019-10-02

### This is the second preview release of *Unity Package Nuget.NewtonsoftJson* v1.0.1.

* Use .net standard 2.0 version of the dll.

## [1.0.1-preview.1] - 2019-08-13

### This is the first preview release of *Unity Package Nuget.Newtonsoft.Json* v1.0.1.

* Renamed the dll to match the assembly name. Users will need to update their assembly references.

## [1.0.0] - 2019-08-08

### This is the first release of *Unity Package Nuget.Newtonsoft.Json*.

* No notable changes from the previous preview release.

## [1.0.0-preview.4] - 2019-08-06

### This is the fourth preview release of *Unity Package Nuget.Newtonsoft.Json*.

* Remove the unused asmdef file and make it such that the dll needs to be explicitly included.

## [1.0.0-preview.3] - 2019-07-03

### This is the third preview release of *Unity Package Nuget.Newtonsoft.Json*.

* Update the documentation for the package and the package description to state that this package is for internal Unity
  use __only__.

## [1.0.0-preview.2] - 2019-06-21

### This is the second preview release of *Unity Package Nuget.NewtonsoftJson*.

* The name of the package has been changed to avoid a naming issue with npm.

## [1.0.0-preview.1] - 2019-06-21

### This is the first preview release of *Unity Package Nuget.NewtonsoftJson*.

* This is the first preview of a the custom Unity package for NewtonsoftJson. Please report any bugs.
