.noise-window
{
    flex-direction: row;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    position: absolute;
}

.noise-window__settings-container
{
    flex-direction: column;
    width: 350px;
    top: 0;
    bottom: 0;
}

.noise-window__noise-asset-field-container
{
    height: 30px;
    min-height: 30px;
    max-height: 30px;
}

.noise-window__noise-asset-field
{
    padding: 8px 8px 8px 8px;
}

.noise-window__settings-scrollview
{
    width: auto;
    top: 0;
    bottom: 0;
}

.noise-window__noise-gui-container
{
    width: auto;
}

.noise-window__preview-container
{
    flex-grow: 1;
    background-color: #999;
}

.noise-window__preview-container-texture
{
    flex: 1;
    margin: 10px 10px 40px 10px;
}

.noise-window__preview-container-texture, .unity-image
{
    cursor: pan;
}

.noise-window__preview-texture-label
{
    padding: 10px;
}

.noise-window__file-panel-container
{
    padding: 0px 0px 10px 0px;
}

.noise-window__file-panel-button
{
    flex: .33333;
    background-image: none;
    background-color: #ddd;
    border-color: #aaa;
    border-width: 1px;
    height: 30px;
    min-height: 30px;
    max-height: 30px;
}

.noise-window__file-panel-button:hover
{
    background-color: #e6e6e6;
}

.noise-window__file-panel-button:active
{
    background-color: #fff;
}

.noise-window__flex-area
{
    flex: 1;
}

.noise-window__foldout-container
{
    background-color: #dcdcdc;
    height: 24px;
    min-height: 20px;
    max-height: 20px;
}

.noise-window__flex-half
{
    flex: .5;
}

.noise-window__flex-third
{
    flex: .33333;
}

.noise-window__export-container
{
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    border-color: #aaa;
    border-width: 1px 0 0 0;
    flex-shrink: 0;
}

.noise-window__export-settings
{
    padding: 8px;
}

.noise-window__export-button
{
    background-image: none;
    background-color: #ddd;
    border-color: #aaa;
    border-width: 1px;
    height: 30px;
    min-height: 30px;
    max-height: 30px;
}

.noise-window__export-button:hover
{
    background-color: #e6e6e6;
}

.noise-window__export-button:active
{
    background-color: #fff;
}